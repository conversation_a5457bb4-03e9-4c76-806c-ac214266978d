#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包插件
使用豆包 API对获取的JSON数据进行文案优化
"""

import os
import json
import requests
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
import re
import aiohttp
import time
import ssl
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

class DoubaoPlugin:
    def __init__(self, plugin_config=None):
        default_config = {
            "base_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
        }
        # 如果提供了外部配置，则覆盖默认配置
        self.api_config = default_config.copy()
        if plugin_config:
            self.api_config.update(plugin_config)

    async def optimize_content_sync(self, json_data: Dict[str, Any], cache_key: str = None) -> Dict[str, Any]:
        try:         
            return self._call_doubao_api_sync(json_data, cache_key)           
        except Exception as e:
            error_msg = f"❌ 同步优化失败: {e}"
            raise Exception(error_msg)
    

    def _build_api_request_data(self, content: str) -> tuple:
        headers = {
            "Authorization": f"Bearer {self.api_config['api_key']}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.api_config["model"],
            "messages": [
                 {
                    "role": "system",
                    "content": self.prompt_template
                },
                {
                    "role": "user",
                    "content": content
                }
            ],
            "temperature": self.api_config["temperature"],
            # "max_tokens": self.api_config.get("max_tokens"),  # 使用配置或默认值
            "max_tokens": 32768, #豆包默认是32768,不是48000
            "stream": False
        }
        url = self.api_config['base_url']
        
        return headers, payload, url
    
    def _validate_and_extract_response(self, response_data: dict) -> str:
        """验证并提取API响应内容的通用方法"""
        # 检查响应结构
        if 'choices' not in response_data:
            raise Exception("API响应格式异常：缺少choices字段")
        
        if not response_data['choices']:
            raise Exception("API响应格式异常：choices为空")
        
        if 'message' not in response_data['choices'][0]:
            raise Exception("API响应格式异常：缺少message字段")
        
        content = response_data['choices'][0]['message']['content']
        
        return content

    def _save_raw_response(self, raw_response_text: str, cache_key: str = None) -> None:
        """保存原始API响应文本用于调试分析"""
        try:

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存文件路径
            debug_dir = os.path.join("src", "cache", "ai_optimized", "debug")
            os.makedirs(debug_dir, exist_ok=True)
            
            debug_filename = f"doubao_raw_response_{cache_key}_{timestamp}.txt"
            debug_path = os.path.join(debug_dir, debug_filename)
            
            # 保存原始响应文本
            with open(debug_path, 'w', encoding='utf-8') as f:
                f.write(f"豆包原始响应数据\n")
                f.write(f"时间: {datetime.now().isoformat()}\n")
                f.write(f"缓存键: {cache_key}\n")
                f.write(f"响应长度: {len(raw_response_text)} 字符\n")
                f.write("="*50 + "\n")
                f.write(raw_response_text)
            
            print(f"📝 豆包原始响应已保存: {debug_filename}")
            
        except Exception as e:
            print(f"⚠️ 保存豆包原始响应失败: {e}")

    def _call_doubao_api_sync(self, content: str, cache_key: str = None) -> str:
        headers, payload, url = self._build_api_request_data(content)

        # 创建带重试机制的session
        session = requests.Session()

        # 配置重试策略
        retry_strategy = Retry(
            total=3,  # 总重试次数
            backoff_factor=1,  # 重试间隔倍数
            status_forcelist=[429, 500, 502, 503, 504],  # 需要重试的HTTP状态码
            allowed_methods=["POST"]  # 允许重试的HTTP方法
        )

        # 配置适配器
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # 配置SSL验证（如果需要）
        session.verify = True  # 可以设置为False来跳过SSL验证，但不推荐

        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                print(f"🔄 尝试调用豆包API (第{attempt + 1}次)")

                response = session.post(
                    url,
                    headers=headers,
                    json=payload,
                    timeout=(30, 300),  # (连接超时, 读取超时)
                    stream=False
                )

                if response.status_code == 200:
                    print("✅ 豆包API调用成功")
                    # 保存原始响应文本
                    self._save_raw_response(response.text, cache_key)
                    result = response.json()
                    # 提取响应内容
                    extracted_content = self._validate_and_extract_response(result)
                    return extracted_content
                else:
                    error_msg = f"API调用失败 (状态码: {response.status_code}): {response.text}"
                    print(f"❌ {error_msg}")
                    if attempt == max_attempts - 1:  # 最后一次尝试
                        raise Exception(error_msg)
                    else:
                        print(f"⏳ 等待 {2 ** attempt} 秒后重试...")
                        time.sleep(2 ** attempt)  # 指数退避
                        continue

            except requests.exceptions.SSLError as e:
                error_msg = f"SSL连接错误: {e}"
                print(f"❌ {error_msg}")
                if attempt == max_attempts - 1:
                    raise Exception(f"SSL连接失败，请检查网络连接或证书配置: {e}")
                else:
                    print(f"⏳ SSL错误，等待 {2 ** attempt} 秒后重试...")
                    time.sleep(2 ** attempt)
                    continue

            except requests.exceptions.ConnectionError as e:
                error_msg = f"连接错误: {e}"
                print(f"❌ {error_msg}")
                if attempt == max_attempts - 1:
                    raise Exception(f"网络连接失败，请检查网络连接: {e}")
                else:
                    print(f"⏳ 连接错误，等待 {2 ** attempt} 秒后重试...")
                    time.sleep(2 ** attempt)
                    continue

            except requests.exceptions.Timeout as e:
                error_msg = f"请求超时: {e}"
                print(f"❌ {error_msg}")
                if attempt == max_attempts - 1:
                    raise Exception(f"请求超时，请稍后重试: {e}")
                else:
                    print(f"⏳ 请求超时，等待 {2 ** attempt} 秒后重试...")
                    time.sleep(2 ** attempt)
                    continue

            except requests.RequestException as e:
                error_msg = f"网络请求失败: {e}"
                print(f"❌ {error_msg}")
                if attempt == max_attempts - 1:
                    raise Exception(error_msg)
                else:
                    print(f"⏳ 网络错误，等待 {2 ** attempt} 秒后重试...")
                    time.sleep(2 ** attempt)
                    continue

            except Exception as e:
                error_msg = f"API调用异常: {e}"
                print(f"❌ {error_msg}")
                if attempt == max_attempts - 1:
                    raise Exception(error_msg)
                else:
                    print(f"⏳ 未知错误，等待 {2 ** attempt} 秒后重试...")
                    time.sleep(2 ** attempt)
                    continue

        # 如果所有重试都失败了
        raise Exception("所有重试尝试都失败了")
        
    def init_prompt(self, prompt_file: str="empty"):
        """初始化提示词文件"""
        print(f"🔍 初始化提示词文件: {prompt_file}")
        self.prompt_file = os.path.join("src/prompty", prompt_file+".md")
        self.prompt_template = self._load_prompt()
        
    def _load_prompt(self) -> str:
        if os.path.exists(self.prompt_file):
            with open(self.prompt_file, 'r', encoding='utf-8') as f:
                prompt_content = f.read()
            return prompt_content
        else:
            print(f"❌ 提示词文件不存在: {self.prompt_file}")
            return "请提供驾考文案优化的提示词。"
      